#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python脚本：为TypeScript数组中的每条数据添加dataIndex序号
"""

import re
import json

def add_data_index_to_ts_file(input_file, output_file):
    """
    读取TypeScript文件，为数组中的每个对象添加dataIndex属性，从1开始
    
    Args:
        input_file (str): 输入的TypeScript文件路径
        output_file (str): 输出的TypeScript文件路径
    """
    
    try:
        # 读取原始文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"正在处理文件: {input_file}")
        
        # 找到数组开始的位置
        array_start_pattern = r'const arr: OrganizationData\[\] = \['
        array_start_match = re.search(array_start_pattern, content)
        
        if not array_start_match:
            print("错误：未找到数组定义")
            return False
        
        # 分割内容
        before_array = content[:array_start_match.end()]
        after_array_start = content[array_start_match.end():]
        
        # 找到数组结束位置
        bracket_count = 1
        array_end_pos = 0
        
        for i, char in enumerate(after_array_start):
            if char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
                if bracket_count == 0:
                    array_end_pos = i
                    break
        
        if array_end_pos == 0:
            print("错误：未找到数组结束位置")
            return False
        
        array_content = after_array_start[:array_end_pos]
        after_array = after_array_start[array_end_pos:]
        
        # 处理数组内容
        processed_content = process_array_content(array_content)
        
        # 重新组合内容
        new_content = before_array + processed_content + after_array
        
        # 写入新文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"处理完成！输出文件: {output_file}")
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return False

def process_array_content(array_content):
    """
    处理数组内容，为每个对象添加dataIndex
    
    Args:
        array_content (str): 数组内容字符串
        
    Returns:
        str: 处理后的数组内容
    """
    
    # 使用正则表达式匹配每个对象
    object_pattern = r'\{\s*([^{}]*(?:\{[^{}]*\}[^{}]*)*)\s*\}'
    objects = re.findall(object_pattern, array_content)
    
    if not objects:
        print("警告：未找到任何对象")
        return array_content
    
    print(f"找到 {len(objects)} 个对象")
    
    # 重新构建数组内容
    new_objects = []
    
    for i, obj_content in enumerate(objects, 1):
        # 清理对象内容
        obj_content = obj_content.strip()
        
        # 添加dataIndex属性到对象开头
        new_obj = f"{{ dataIndex: {i}, {obj_content} }}"
        new_objects.append(new_obj)
    
    # 重新组合，保持原有的格式
    result = "\n  " + ",\n  ".join(new_objects) + "\n"
    
    return result

def main():
    """主函数"""
    input_file = "src/pages/Home2/moke/data.ts"
    output_file = "src/pages/Home2/moke/data_with_index.ts"
    
    print("=" * 50)
    print("TypeScript数组dataIndex添加工具")
    print("=" * 50)
    
    # 检查输入文件是否存在
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            pass
    except FileNotFoundError:
        print(f"错误：输入文件 {input_file} 不存在")
        return
    
    # 处理文件
    success = add_data_index_to_ts_file(input_file, output_file)
    
    if success:
        print("\n✅ 处理成功！")
        print(f"📁 原文件: {input_file}")
        print(f"📁 新文件: {output_file}")
        print("\n💡 新文件中每个对象都已添加dataIndex属性（从1开始）")
    else:
        print("\n❌ 处理失败！")

if __name__ == "__main__":
    main()
