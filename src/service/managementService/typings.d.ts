import type { EventTypeEnum, NotifyStatusEnum } from "@/enum";

export namespace ManagementType {
  export interface File {
    /**
     * 附件地址
     */
    attachmentFile?: string;
    /**
     * 附件名称
     */
    filename?: string;
    /**
     * 附件类型
     */
    fileType?: string;
  }

  export interface EventNotifyInfoResult {
    /**
     * 所属区域
     */
    area?: string;
    /**
     * 事件附件列表
     */
    eventAttachmentList?: File[];
    /**
     * 事件描述
     */
    eventDesc?: string;
    /**
     * 事件详情
     */
    eventDetail?: string;
    /**
     * 事件等级 1-4，依次是特别重大事件、重大事件、较大事件、一般事件
     */
    eventLevel?: string;
    /**
     * 事件名称
     */
    eventName?: string;
    /**
     * 事件处理列表
     */
    processInfoDtoList?: ProcessInfoDto[];
  }

  export interface ProcessInfoDto {
    /**
     * 附件列表
     */
    attachmentList?: File[];
    /**
     * 处置意见
     */
    disposalOpinion?: string;
    /**
     * 流程名称
     */
    processName?: string;
    /**
     * 流程时间
     */
    processTime?: string;
  }

  export interface EventNotifyListResult {
    /**
     * 事件列表
     */
    eventNotifyList?: EventNotifyDto[];
    /**
     * 总记录数
     */
    total?: number;
  }

  export interface ProcessDto {
    /**
     * 流程名称
     */
    processName: string;

    /**
     * 流程时间
     */
    processTime: string;
  }

  export interface EventNotifyDto {
    /**
     * 事件Id
     */
    eventId: number;
    /**
     * 通报id
     */
    notifyId: string;
    /**
     * 事件名称
     */
    eventName?: string;
    /**
     * 流程列表
     */
    processDtoList?: ProcessDto[];
    /**
     * 标题
     */
    title?: string;
  }

  export interface EventNotifyOverviewResult {
    /**
     * 处置率
     */
    archiveRate?: number;
    /**
     * 通报事件数
     */
    eventNotifyCount?: number;

    /**
     * 响应率
     */
    feedbackRate?: number;

    /**
     * 预警事件总数
     */
    riskWarmCount?: number;

    /**
     * 协同事件总数
     */
    collaborativeTask?: number
  }

  export interface EventTypeRankDto {
    /**
     * 事件类型
     */
    eventType?: string;
    /**
     * 事件数量
     */
    eventCount?: number;
  }

  export interface EventTypeRankResult {
    /**
     * 应急指挥事件类型排名列表
     */
    eventTypeRankDtoList: EventTypeRankDto[];
  }

  export interface FeedbackRateRankParams extends API.ListParams {
    /**
     * 查询类型
     */
    type: string;
  }

  export interface FeedbackRateRankDto {
    /**
     * 地市/行业名称
     */
    cityOrIndustryName: string;
    /**
     * 事件数量
     */
    eventCount: number;
    /**
     * 反馈事件数量
     */
    feedbackEventCount: number;
    /**
     * 反馈率
     */
    feedbackRate: string;
  }

  export interface FeedbackRateRankResult {
    /**
     * 反馈率排名列表
     */
    feedbackRateRankDtoList: FeedbackRateRankDto[];

    /**
     * 总数
     */
    totalCount: number;
  }

  export interface NotifyCountTop5Params extends API.ListParams {
    /**
     * 查询类型1-地市 2-行业
     */
    type: string;
  }

  export interface RealtimeEventDto {
    /**
     * 事件所属区域
     */
    eventArea?: string;
    /**
     * 事件等级  1-4，依次是特别重大事件、重大事件、较大事件、一般事件
     */
    eventLevel?: string;
    /**
     * 事件状态 0-待下发 1-待审核 2-待签收 3-待反馈 4-待复核 5-待归档 6-已归档
     */
    eventStatus?: string;
    /**
     * 事件类型
     */
    eventType?: string;
    /**
     * 事件发现时间
     */
    foundTime?: string;
    /**
     * 单位行业
     */
    unitIndustry?: string;
    /**
     * 单位名称
     */
    unitName?: string;
    /**
     * 通报id
     */
    notifyId: string;
    /**
     * 事件内容 名称
     */
    eventName?: string;
    /**
     * 通知状态
     */
    notifyStatus: NotifyStatusEnum;
  }

  export interface RealtimeEventListParams extends API.ListParams {
    /**
     * 开始时间
     */
    startTime?: string;

    /**
     * 结束时间
     */
    endTime?: string;

    /**
     * 单位行业
     */
    unitIndustry?: string;

    /**
     * 事件类型
     */
    eventType?: string;

    /**
     * 事件等级
     */
    eventLevel?: string;

    /**
     * 事件状态
     */
    eventStatus?: string;

    /**
     * 事件所属省份
     */
    eventProvince?: string;

    /**
     * 事件所属地市
     */
    eventCity?: string;

    /**
     * 事件所属区县
     */
    eventCounty?: string;
  }

  export interface RealtimeEventListResult {
    /**
     * 快处置实时事件列表
     */
    realtimeEventDtoList?: RealtimeEventDto[];
    /**
     * 总记录数
     */
    total?: number;
  }

  export interface NotifyCountDto {
    /**
     * 次数
     */
    count: number;
    /**
     * 地市/行业名称
     */
    name: string;
  }

  export interface NotifyCountTop5Result {
    /**
     * 通报次数top5
     */
    notifyCountDtoList: NotifyCountDto[];

    /**
     * 总条数
     */
    total: number;

    /**
     * 最大次数
     */
    valueBig: number;
  }



  export interface ThreeLevelInfoResult {
    /**
     * 协同指令数量
     */
    collaborationInstruction: number;

    /**
     * 处置数量
     */
    disposeOfCount: number;

    /**
     * 事件指令数量
     */
    eventInstructionCount: number;

    /**
     * 预警数量
     */
    forewarningCount: number;

    /**
     * 通报数量
     */
    notificationCount: number;

    /**
     * 风险指令数量
     */
    riskInstruction: number;
  }

  export interface EventStatusItem {
    /**
     * 事件枚举值
     */
    eventMenuCode: string;

    /**
     * 事件枚举名称
     */
    eventMenuName: string
  }

  export interface GetEventMenuResult {
    /**
     * 菜单列表
     */
    eventStatusList: EventStatusItem[];
  }

  export interface PositionItem {
    /**
     * 位置名称
     */
    children?: PositionItem[];

    /**
     * 位置编码
     */
    positionCode: string;

    /**
     * 位置名称
     */
    positionName: string;

    /**
     * 父位置编码
     */
    positionParentCode: string
  }

  export interface GetPositionListResult {
    /**
     * 位置列表
     */
    positionList: PositionItem[];
  }

  export interface PreviewPdfParams {
    /**
     * 文件地址
     */
    filePath: string;

    /**
     * 文件类型
     */
    fileType: string;
  }

}

declare global {
  namespace API {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export import Management = ManagementType;
  }
}
