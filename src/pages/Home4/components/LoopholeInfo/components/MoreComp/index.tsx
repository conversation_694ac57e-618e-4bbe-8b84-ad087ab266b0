import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import { VulLevelEnum } from "@/enum";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "vulName",
      title: "漏洞名称",
    },
    {
      dataIndex: "vulCategory",
      title: "漏洞类别",
      valueType: "select",
      valueEnum: {
        commonVul: "通用型漏洞",
        eventVul: "事件型漏洞",
      },
    },
    {
      dataIndex: "vulTypeName",
      title: "漏洞类型",
    },
    {
      dataIndex: "discoveryTime",
      title: "发现时间",
    },
    {
      dataIndex: "unitName",
      title: "单位",
    },
    {
      dataIndex: "vulLevel",
      title: "漏洞等级",
      valueType: "select",
      valueEnum: {
        [VulLevelEnum.Low]: "低危",
        [VulLevelEnum.Middle]: "中危",
        [VulLevelEnum.High]: "高危",
        [VulLevelEnum.Super]: "超危",
        [VulLevelEnum.High5]: "高危5",
        [VulLevelEnum.Middle6]: "中危6",
        [VulLevelEnum.Low7]: "低危7",
        [VulLevelEnum.Super8]: "超危8",
      },
    },
    {
      dataIndex: "vulVendor",
      title: "漏洞厂商",
    },
    {
      dataIndex: "vulDescription",
      title: "漏洞描述",
    },
    {
      dataIndex: "vulUrl",
      title: "漏洞URL",
    },
    {
      dataIndex: "CNNVD",
      title: "CNNVD编号",
    },
    {
      dataIndex: "CVE",
      title: "CVE编号",
    },
    {
      dataIndex: "2",
      title: "漏洞描述",
    },
    {
      dataIndex: "3",
      title: "解决方案",
    }
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await synergyService.vulInfoList({
      startTime,
      endTime,
      ...params,
    });
    return {
      data: res.vulInfoListDtoList ?? [],
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="漏洞信息列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
