import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "eventName",
      title: "事件名称",
    },
    {
      dataIndex: "eventType",
      title: "事件类型",
    },
    {
      dataIndex: "eventLevel",
      title: "事件等级",
    },
    {
      dataIndex: "foundTime",
      title: "发现时间",
      width: 220,
    },
    {
      dataIndex: "uniName",
      title: "涉事单位",
    },
    {
      dataIndex: "assessStatus",
      title: "研判状态",
      valueType: "select",
      valueEnum: {
        true: "已研判",
        false: "未研判",
      },
    },
    {
      dataIndex: "assessResult",
      title: "研判结果",
      valueType: "select",
      valueEnum: {
        success: "确认攻击",
        fail: "标记误报",
      },
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await synergyService.eventInfoList({
      startTime,
      endTime,
      ...params,
    });
    return {
      data: res.eventInfoListDtoLit ?? [],
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="事件信息列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
