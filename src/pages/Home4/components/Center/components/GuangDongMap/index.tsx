import * as THREE from "three";
import { Canvas } from "@react-three/fiber";
import ScaleCanvas from "@/components/Map/ScaleCanvas";
import {
  OrbitControls,
  PerspectiveCamera,
  useProgress,
  // Stats,
} from "@react-three/drei";
import BasicMap from "./components/BasicMap";
import Container from "./container";
import { memo, useMemo } from "react";
import React from "react";
import { Spin } from "antd";
import SubComponent from "./components/SubComponent";

function GuangDongMap() {
  const width = 1920;
  const height = 1080;

  const progress = useProgress();

  const spinning = useMemo(() => progress.progress < 100, [progress.progress]);

  return (
    <React.Fragment>
      {/* <img src={mapBgUrl} className="opacity-40 w-[676px] absolute x-centered mt-[200px] ml-[-22px]" /> */}
      <div className="absolute z-[9]">
        <Canvas
          gl={{
            //  设备像素比 不同硬件设备的屏幕window.devicePixelRatio的值可能不同 设置是为了适应不同的硬件设备屏幕 min是为了防止设备像素比过大导致渲染器内存溢出
            pixelRatio: Math.min(window.devicePixelRatio, 2),
            // 色调映射
            toneMapping: THREE.NoToneMapping,
          }}
          className="absolute x-centered ml-[0px] top-[-90px]"
          style={{
            // pointerEvents: "auto",
            userSelect: "none",
            opacity: spinning ? 0 : 1,
          }}
        >
          <ScaleCanvas width={width} height={height} />
          <PerspectiveCamera
            near={1}
            far={1000}
            makeDefault
            up={[0, 1, 0]}
            fov={54}
            position={[0, 300, 0]}
          />
          <group rotation={[-Math.PI / 1.5, 0, 0]}>
            {/* 地图 */}
            <BasicMap />
            <SubComponent />
          </group>

          <ambientLight color={"#FFFFFF"} intensity={3} />
          <OrbitControls
            makeDefault
            enableDamping={false}
            enablePan={false}
            enableRotate={false}
            enableZoom={false}
            zoomSpeed={0.2}
            maxZoom={0.1}
          />
          {/* <Stats /> */}
        </Canvas>
      </div>
      <Spin spinning={spinning} className="x-centered z-[100] top-[400px]" />
    </React.Fragment>
  );
}
export default memo(() => {
  return (
    <Container.Provider>
      <GuangDongMap />
    </Container.Provider>
  );
});
