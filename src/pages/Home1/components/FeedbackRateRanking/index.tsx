import Card from "@/components/Card";
import { useMemo, useState } from "react";
import useVisible from "@/hooks/useVisible";
import MoreComp from "./components/MoreComp";
import List from "./components/List";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import { Pagination } from "antd";

function FeedbackRateRanking() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { startTime, endTime } = useTime();

  const [activeKey, setActiveKey] = useState("1");

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const { data: feedbackData, loading: feedbackLoading } = useRequest(
    () =>
      managementService.feedbackRateRank({
        startTime,
        endTime,
        page: current,
        size: pageSize,
        type: activeKey,
      }),
    {
      refreshDeps: [startTime, endTime, current, pageSize, activeKey],
      ready: activeKey === "1" || activeKey === "2",
    }
  );

  const { data: notifyData, loading: notifyLoading } = useRequest(
    () =>
      managementService.notifyCountTop5({
        startTime,
        endTime,
        page: current,
        size: pageSize,
        type: "1", // "地市" type
      }),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
      ready: activeKey === "0",
    }
  );

  const list = useMemo(() => {
    if (activeKey === "0") {
      return notifyData?.notifyCountDtoList?.map((val) => ({
        name: val.name,
        value: val.count
      })) ?? [];
    }
    // TODO:8月15临时修改
    return feedbackData?.feedbackRateRankDtoList.map((val) => ({
      ...val,
      feedbackRate:"100"
    })) ?? [];
  }, [activeKey, feedbackData?.feedbackRateRankDtoList, notifyData?.notifyCountDtoList]);

  const items = useMemo(() => {
    return [
      {
        key: "0",
        label: "通报数",
      },
      {
        key: "1",
        label: "处置率",
      },
      {
        key: "2",
        label: "响应率",
      },
    ];
  }, []);

  const total = useMemo(() => {
    if (activeKey === "0") {
      return notifyData?.total ?? 0;
    }
    return feedbackData?.totalCount ?? 0;
  }, [activeKey, feedbackData?.totalCount, notifyData?.total]);

  const maxValue = useMemo(() => {
    if (activeKey === "0") {
      return notifyData?.valueBig ?? 0;
    }
    return 0;
  }, [activeKey, notifyData?.valueBig]);

  const loading = activeKey === "0" ? notifyLoading : feedbackLoading;

  const rankOffset = useMemo(() => (current - 1) * pageSize, [current, pageSize]);

  return (
    <div className="absolute left-[46px] top-[733px] h-[540px]">
      <MoreComp
        type={activeKey}
        open={open}
        onCancel={setFalse}
        key={visibleKey}
      />
      <Card
        headerType={1}
        title={"地市排行"}
        bodyClass="!py-1"
        tabsProps={{
          items,
          activeKey,
          onChange: (key) => {
            setCurrent(1);
            setPageSize(5);
            setActiveKey(key);
          },
        }}
        loading={loading}
        // TODO:8月15临时修改
        // onMore={setTrue}
      >
        <List 
          data={list} 
          key={JSON.stringify(list)} 
          type={activeKey} 
          maxValue={activeKey === "0" ? maxValue : undefined}
          rankOffset={rankOffset}
        />
        {total > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, pageSize) => {
                setCurrent(page);
                setPageSize(pageSize);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default FeedbackRateRanking;
