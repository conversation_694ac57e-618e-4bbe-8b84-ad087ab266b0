import { motion } from "framer-motion";
import { useMemo } from "react";

interface NotificationCountItem {
  name: string;
  value: number;
}

interface IProps {
  index: number;

  rowGap: number;

  itemHeight: number;

  item: API.Management.FeedbackRateRankDto | NotificationCountItem;

  // 是否动画
  showAnimate?: boolean;
  
  // 类型: 0 通报数 1 处置率 2响应率
  type: string;
  
  // 通报数最大值
  maxValue?: number;

  // 全局排名偏移量（用于分页后计算正确的 TopN）
  rankOffset?: number;
}

function ListItem(props: IProps) {
  const {
    index,
    rowGap,
    itemHeight,
    item,
    type,
    maxValue = 100,
    showAnimate = true,
    rankOffset = 0,
  } = props;
  
  const isNotificationCount = type === "0";
  
  // 获取城市名称和显示值
  const cityName = isNotificationCount 
    ? (item as NotificationCountItem).name 
    : (item as API.Management.FeedbackRateRankDto).cityOrIndustryName;
    
  const displayValue = isNotificationCount 
    ? (item as NotificationCountItem).value 
    : (item as API.Management.FeedbackRateRankDto).feedbackRate;
  
  // 计算宽度百分比
  const widthPercentage = isNotificationCount 
    ? ((displayValue as number) / maxValue) * 100 
    : displayValue as number;

  // 全局索引（含分页偏移）
  const globalIndex = rankOffset + index;

  const animate = useMemo(() => {
    return {
      width: `${widthPercentage}%`,
      transition: {
        duration: showAnimate ? 1 : 0,
      },
    };
  }, [widthPercentage, showAnimate]);

  const indexStyle: React.CSSProperties = useMemo(() => {
    switch (globalIndex) {
      case 0:
        return {
          border: "1px solid rgba(243, 182, 123, 1)",
          background: "rgba(243, 182, 123, 0.65)",
        };
      case 1:
        return {
          border: "1px solid rgba(255, 255, 128, 1)",
          background: "rgba(255, 255, 128, 0.65)",
        };
      case 2:
        return {
          border: "1px solid rgba(111, 184, 255, 1)",
          background: "rgba(111, 184, 255, 0.65)",
        };

      default:
        return {
          background: "rgba(20, 62, 127, 1)",
        };
    }
  }, [globalIndex]);

  const barStyle: React.CSSProperties = useMemo(() => {
    switch (globalIndex) {
      case 0:
        return {
          background:
            "linear-gradient(90deg,rgba(159, 124, 91, 0.79),rgba(243, 182, 123, 0.79)",
        };
      case 1:
        return {
          background:
            "linear-gradient(90deg,rgba(167, 171, 94, 0.79),rgba(255, 255, 128, 0.79)",
        };
      case 2:
        return {
          background:
            "linear-gradient(90deg,rgba(73, 126, 180, 0.79),rgba(111, 184, 255, 0.79)",
        };
      default:
        return {
          background:
            "linear-gradient(90deg,rgba(14, 50, 102, 0.79),rgba(73, 125, 177, 0.79)",
        };
    }
  }, [globalIndex]);

  const valueStyle: React.CSSProperties = useMemo(() => {
    switch (globalIndex) {
      case 0:
        return {
          background:
            "linear-gradient(90deg,rgba(243, 182, 123, 0.45),rgba(243, 182, 123, 0.05)",
        };
      case 1:
        return {
          background:
            "linear-gradient(90deg,rgba(167, 171, 94, 0.55),rgba(167, 171, 94, 0.05)",
        };
      case 2:
        return {
          background:
            "linear-gradient(90deg,rgba(111, 184, 255, 0.45),rgba(111, 184, 255, 0.05)",
        };
      default:
        return {
          background:
            "linear-gradient(90deg,rgba(20, 62, 127, 0.45),rgba(20, 62, 127, 0.05)",
        };
    }
  }, [globalIndex]);

  return (
    <div
      key={`item${index}`}
      className="flex items-center gap-x-2 cursor-pointer hover:bg-[rgba(57,90,191,.25)]"
      style={{
        marginBottom: rowGap,
        height: itemHeight,
      }}
    >
      <div className="flex-1 h-full px-2 flex items-center gap-x-4 border border-[rgba(197,208,212,.15)]">
        <div
          className="w-[66px] min-w-[66px] h-[26px] color-text flex items-center justify-center"
          style={{
            ...indexStyle,
          }}
        >
          Top {(globalIndex + 1) < 10 ? 0 : ""}
          {globalIndex + 1}
        </div>
        <div className="w-[50px] truncate">{cityName}</div>
        <div className="h-[8px] flex-1 flex items-center bg-[#232E40] relative rounded-[3px] overflow-hidden">
          <motion.div
            className="h-full rounded-[3px]"
            style={barStyle}
            animate={animate}
          ></motion.div>
        </div>
      </div>
      <div
        className="text-white text-[18px] w-[68px] h-full items-center justify-center flex"
        style={{
          fontFamily: "DINCond-Bold",
          ...valueStyle,
        }}
      >
        {isNotificationCount ? displayValue : `${displayValue}%`}
      </div>
    </div>
  );
}
export default ListItem;
