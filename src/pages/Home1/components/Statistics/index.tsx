import iconBg4 from "@/assets/1/bg4.png";
import iconBg5 from "@/assets/1/bg5.png";
import icon2 from "@/assets/1/2.png";
import icon3 from "@/assets/1/3.png";
import bg8 from "@/assets/1/8.png";
import React, { useCallback } from "react";
import { useRequest } from "ahooks";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";
import { Spin } from "antd";
import { EventTypeEnum } from "@/enum";
import Container from "../../container";

function Statistics() {
  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    async () =>
      managementService.eventNotifyOverview({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const { onShowList } = Container.useContainer();

  const onDetail = useCallback(
    (value: EventTypeEnum) => {
      onShowList({
        type: value,
      });
    },
    [onShowList]
  );

  return (
    <React.Fragment>
      <div className="absolute left-[58px] top-[124px] w-[440px] h-[248px]">
        <Spin spinning={loading}>
          {/* <div className="flex items-center relative mt-1">
            <div
              className="w-[138px] h-[124px] ml-[-20px] mt-[-10px] bg-cover text-center"
              style={{
                backgroundImage: `url(${ionBg1})`,
              }}
            >
              <img
                className="inline-block w-[70px] animate-[moveOpacity_2s_ease-in-out_infinite]"
                src={icon1}
                alt=""
              />
            </div>
            <div>
              <BgImage
                className="w-[334px] absolute top-0 h-[116px] flex items-center justify-center flex-col cursor-pointer"
                url={iconBg2}
                onClick={() => onDetail(EventTypeEnum.All)}
              >
                <div className="text-[24px] color-secondary]">通报事件总数</div>
                <div className="text-[50px] color-secondary leading-[46px] color-text font-[DINCond-Bold]">
                  {data?.eventNotifyCount}
                </div>
              </BgImage>
            </div>
          </div> */}
          <div
            className="h-[136px] px-[24px] relative flex flex-col cursor-pointer"
            style={{
              background: `url(${bg8}) no-repeat center center / 100% 100%`,
            }}
            onClick={() => onDetail(EventTypeEnum.All)}
          >
            <div className="w-full flex flex-col">
              <div className="w-full h-[58px] grid grid-cols-3 relative">
                <div className="text-[#ADC9E9] text-center leading-[76px] z-[2]">
                  通报事件总数
                </div>
                <div className="text-[#ADC9E9] text-center leading-[76px] z-[2]">
                  预警事件总数
                </div>
                <div className="text-[#ADC9E9] text-center leading-[76px] z-[2]">
                  协同事件总数
                </div>
                <div className="w-full h-[20px] bg-[#3152AC] absolute bottom-[0px]"></div>
              </div>
              <div className="w-full grid grid-cols-3">
                <div
                  className="text-center text-[#F1BB80] text-[28px]"
                  style={{
                    fontFamily: "DINCond-Bold",
                  }}
                >
                  {data?.eventNotifyCount}
                </div>
                <div
                  className="text-center text-[#A3D588] text-[28px]"
                  style={{
                    fontFamily: "DINCond-Bold",
                  }}
                >
                  {data?.riskWarmCount}
                </div>
                <div
                  className="text-center text-[#6AA3E8] text-[28px]"
                  style={{
                    fontFamily: "DINCond-Bold",
                  }}
                >
                  {data?.collaborativeTask}
                </div>
              </div>
            </div>
          </div>
          <div className="flex mt-[10px] gap-x-2">
            <div
              className="flex cursor-pointer"
              onClick={() => onDetail(EventTypeEnum.Responded)}
            >
              <div
                className="w-[106px] h-[106px] flex items-center justify-center"
                style={{
                  backgroundImage: `url(${iconBg4})`,
                }}
              >
                <img src={icon2} alt="" className="w-[94px] -mt-2" />
              </div>
              <div
                className="w-[114px] h-[106px] text-center"
                style={{
                  backgroundImage: `url(${iconBg5})`,
                }}
              >
                <div className="color-secondary leading-[40px]">响应率</div>
                <div className="color-text leading-[56px] text-[32px] font-[DINCond-Bold]">
                  {/* {data?.feedbackRate}% */}
                  100%
                </div>
              </div>
            </div>
            <div
              className="flex cursor-pointer"
              onClick={() => onDetail(EventTypeEnum.Dealt)}
            >
              <div
                className="w-[106px] h-[106px] flex items-center justify-center"
                style={{
                  backgroundImage: `url(${iconBg4})`,
                }}
              >
                <img src={icon3} alt="" className="w-[94px] -mt-2" />
              </div>
              <div
                className="w-[114px] h-[106px] text-center"
                style={{
                  backgroundImage: `url(${iconBg5})`,
                }}
              >
                <div className="color-secondary leading-[40px]">处置率</div>
                <div className="color-text leading-[56px] text-[32px] font-[DINCond-Bold]">
                  {/* {data?.archiveRate}% */}
                  {/* TODO:8月15临时修改 */}
                  100%
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </div>
    </React.Fragment>
  );
}
export default Statistics;
