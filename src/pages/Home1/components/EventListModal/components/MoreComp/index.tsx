import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import dictInfo from "@/dictInfo";
import { EventMenuTypeEnum, type EventTypeEnum } from "@/enum";
import Container from "@/pages/Home1/container";
import managementService from "@/service/managementService";
import type { ActionType } from "@ant-design/pro-table";
import { useRequest } from "ahooks";
import type { GetProp } from "antd";
import { memo, useRef, useState } from "react";
import type { IProps as TableProps } from "@/components/Table";
import { s } from "framer-motion/client";
import { useTime } from "@/store/useTime";
import { omit } from "lodash-es";

interface IProps extends CustomModalProps {
  /**
   * 查询类型
   */
  type?: EventTypeEnum;

  /**
   * 地市名称
   */
  city?: string;

  /**
   * 行业名称
   */
  industry?: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, ...rest } = props;
  const actionRef = useRef<ActionType>();
  const { onShowDetail, params } = Container.useContainer();
  const [collapsed, setCollapsed] = useState(false);

  const { data: nacIndustryCategoryList } = useRequest(() =>
    managementService.getEventMenu(EventMenuTypeEnum.NacIndustryCategory)
  );

  const { data: networkSecEventTypeList } = useRequest(() =>
    managementService.getEventMenu(EventMenuTypeEnum.NetworkSecEventType)
  );

  const { data: positionList } = useRequest(() =>
    managementService.getPositionList()
  );

  const { startTime , endTime } = useTime();

  const columns: TableProps['searchColumns'] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
    },
    {
      dataIndex: "foundTime",
      title: "事件发生时间",
      width: 200,
    },
    {
      dataIndex: "unitName",
      title: "单位名称",
    },
    {
      dataIndex: "eventType",
      title: "事件类型",
    },
    {
      dataIndex: "eventLevel",
      title: "事件等级",
      valueType: "select",
      fieldProps: {
        options: dictInfo.realtimeEventLevel,
      },
    },
    {
      dataIndex: "eventStatus",
      title: "事件状态",
      valueType: "select",
      fieldProps: {
        options: dictInfo.realtimeEventStatus,
      },
    },
    {
      dataIndex: "unitIndustry",
      title: "单位行业",
    },
    {
      dataIndex: "eventArea",
      title: "事件所属区域",
    },
    {
      dataIndex: "notifyId",
      title: "操作",
      render: (_, record) => {
        return (
          <a
            className="text-[#395ABF]"
            onClick={() => {
              onShowDetail(record.notifyId);
            }}
          >
            详情
          </a>
        );
      },
    },
  ];

  const loadList: GetProp<typeof ProTable, "request"> = async (
    listParams: any,
  ) => {
    const res = await managementService.realtimeEventList({
      ...omit(listParams,"time"),
      ...rest,
      ...params,
      startTime: listParams.time?.[0],
      endTime: listParams.time?.[1],
    } as any);
    return {
      data: res.realtimeEventDtoList ?? [],
      total: res.total,
    };
  };
  
  const searchColumns = [
    {
      dataIndex: "unitIndustry",
      valueType: "select",
      fieldProps: {
        options: nacIndustryCategoryList?.eventStatusList?.map((item) => ({
          label: item.eventMenuName,
          value: item.eventMenuCode,
        })),
      },
      title: "单位行业",
    },
    {
      dataIndex: "time",
      valueType: "dateRange",
      title: "事件发生时间",
    },
    {
      dataIndex: "eventType",
      valueType: "select",
      fieldProps: {
        options: networkSecEventTypeList?.eventStatusList?.map((item) => ({
          label: item.eventMenuName,
          value: item.eventMenuCode,
        })),
      },
      title: "事件类型",
    },
    {
      dataIndex: "eventLevel",
      valueType: "select",
      fieldProps: {
        options: dictInfo.realtimeEventLevel,
      },
      title: "事件等级",
    },
    {
      dataIndex: "eventStatus",
      valueType: "select",
      fieldProps: {
        options: dictInfo.realtimeEventStatus,
      },
      title: "事件状态",
    },
    {
      dataIndex: "eventArea",
      valueType: "select",
      title: "事件所属区域",
      fieldProps: {
        treeData: positionList?.positionList,
        fieldNames: {
          label: "positionName",
          value: "positionCode",
          children: "children",
        },
      },
    },
  ];

  return (
    <ProModal type={2} title="应急指挥实时事件" open={open} onCancel={onCancel}>
      {/* <div className="flex items-center mb-4">
        {tabList?.map((val) => (
          <div
            key={val.value}
            onClick={() =>
              setParams({
                type: val.value,
              })
            }
            className={`${params.type === val.value ? "bg-[#165184] color-text" : "bg-[#27476A] color-secondary"} border border-[#3678BC] w-[90px] h-[36px] flex items-center justify-center cursor-pointer hover:bg-[#165184] color-text`}
          >
            {val.label}
          </div>
        ))}
      </div> */}
      <ProTable
        columns={columns}
        searchColumns={searchColumns as any}
        form={{
          labelWidth: 120,
          size: "large",
          initialValues: {
            time: [startTime, endTime],
          },
        }}
        search={{
          collapsed,
          onCollapse: (e) => {
            setCollapsed(e);
          },
        }}
        request={open ? loadList : undefined}
        params={params}
        actionRef={actionRef}
        scroll={{
          y: 480,
        }}
      />
    </ProModal>
  );
}

export default memo(MoreComp);
