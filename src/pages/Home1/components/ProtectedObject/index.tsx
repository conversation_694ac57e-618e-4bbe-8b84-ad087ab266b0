import { useMemo, useState } from "react";
import { useRequest } from "ahooks";
import { Pagination } from "antd";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import Card from "@/components/Card";
import List from "./components/List";
import MoreComp from "./components/MoreComp";

function ProtectedObject() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { startTime, endTime } = useTime();

  const [activeKey, setActiveKey] = useState("1");

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  // 模拟数据
  const mockData = {
    party: {
      notifyCountDtoList: [
        { name: "教育部", count: 100 },
        { name: "工信部", count: 80 },
        { name: "农业部", count: 20 },
        { name: "发改委", count: 40 },
        { name: "网信部", count: 30 },
        { name: "运输部", count: 24 },
      ],
      total: 6,
      valueBig: 100,
    },
    media: {
      notifyCountDtoList: [
        { name: "人民日报", count: 85 },
        { name: "新华社", count: 72 },
        { name: "央视网", count: 65 },
        { name: "光明日报", count: 58 },
        { name: "经济日报", count: 45 },
      ],
      total: 5,
      valueBig: 85,
    },
    publicService: {
      notifyCountDtoList: [
        { name: "政务服务网", count: 95 },
        { name: "公积金中心", count: 78 },
        { name: "社保局", count: 66 },
        { name: "税务局", count: 54 },
        { name: "工商局", count: 42 },
      ],
      total: 5,
      valueBig: 95,
    },
    industry: {
      notifyCountDtoList: [
        { name: "电力系统", count: 88 },
        { name: "金融机构", count: 76 },
        { name: "通信运营商", count: 69 },
        { name: "交通运输", count: 52 },
        { name: "水利设施", count: 38 },
      ],
      total: 5,
      valueBig: 88,
    },
  };

  // 党政机关数据
  const { data: partyData, loading: partyLoading } = useRequest(
    () => Promise.resolve(mockData.party),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
      ready: activeKey === "1",
    }
  );

  // 媒体数据
  const { data: mediaData, loading: mediaLoading } = useRequest(
    () => Promise.resolve(mockData.media),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
      ready: activeKey === "2",
    }
  );

  // 公共服务数据
  const { data: publicServiceData, loading: publicServiceLoading } = useRequest(
    () => Promise.resolve(mockData.publicService),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
      ready: activeKey === "3",
    }
  );

  // 重点行业数据
  const { data: industryData, loading: industryLoading } = useRequest(
    () => Promise.resolve(mockData.industry),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
      ready: activeKey === "4",
    }
  );

  const list = useMemo(() => {
    let data: API.Management.NotifyCountDto[] | undefined;
    switch (activeKey) {
      case "1":
        data = partyData?.notifyCountDtoList;
        break;
      case "2":
        data = mediaData?.notifyCountDtoList;
        break;
      case "3":
        data = publicServiceData?.notifyCountDtoList;
        break;
      case "4":
        data = industryData?.notifyCountDtoList;
        break;
      default:
        data = [];
    }
    return data?.map((val: API.Management.NotifyCountDto) => ({
      name: val.name,
      value: val.count
    })) ?? [];
  }, [activeKey, partyData?.notifyCountDtoList, mediaData?.notifyCountDtoList, publicServiceData?.notifyCountDtoList, industryData?.notifyCountDtoList]);

  const items = useMemo(() => {
    return [
      {
        key: "1",
        label: "党政",
      },
      {
        key: "2",
        label: "媒体",
      },
      {
        key: "3",
        label: "公共服务",
      },
      {
        key: "4",
        label: "重点行业",
      },
    ];
  }, []);

  const loading = useMemo(() => {
    switch (activeKey) {
      case "1":
        return partyLoading;
      case "2":
        return mediaLoading;
      case "3":
        return publicServiceLoading;
      case "4":
        return industryLoading;
      default:
        return false;
    }
  }, [activeKey, partyLoading, mediaLoading, publicServiceLoading, industryLoading]);

  const total = useMemo(() => {
    switch (activeKey) {
      case "1":
        return partyData?.total ?? 0;
      case "2":
        return mediaData?.total ?? 0;
      case "3":
        return publicServiceData?.total ?? 0;
      case "4":
        return industryData?.total ?? 0;
      default:
        return 0;
    }
  }, [activeKey, partyData?.total, mediaData?.total, publicServiceData?.total, industryData?.total]);

  const maxValue = useMemo(() => {
    switch (activeKey) {
      case "1":
        return partyData?.valueBig ?? 100;
      case "2":
        return mediaData?.valueBig ?? 100;
      case "3":
        return publicServiceData?.valueBig ?? 100;
      case "4":
        return industryData?.valueBig ?? 100;
      default:
        return 100;
    }
  }, [activeKey, partyData?.valueBig, mediaData?.valueBig, publicServiceData?.valueBig, industryData?.valueBig]);

  return (
    <div className="absolute left-[46px] top-[402px]">
      <MoreComp
        type={activeKey}
        open={open}
        onCancel={setFalse}
        key={visibleKey}
      />
      <Card
        headerType={1}
        title={"重点防护对象"}
        bodyClass="!py-1"
        tabsProps={{
          items,
          activeKey,
          onChange: setActiveKey,
        }}
        loading={loading}
        onMore={setTrue}
      >
        <List
          data={list}
          key={JSON.stringify(list)}
          type={activeKey}
          maxValue={maxValue}
          onOpenMore={() => {
            setTrue();
          }}
        />
        {total > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, pageSize) => {
                setCurrent(page);
                setPageSize(pageSize);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}

export default ProtectedObject;
