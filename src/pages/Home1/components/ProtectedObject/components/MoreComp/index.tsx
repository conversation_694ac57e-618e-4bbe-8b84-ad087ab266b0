import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { memo, useMemo } from "react";

interface IProps extends CustomModalProps {
  /**
   * 防护对象类型
   */
  type: string;
}

// 定义通报数据类型
interface NotifyReportDto {
  id: string;
  notifyTime: string;
  notifyUnit: string;
  involvedUnit: string;
  notifyTitle: string;
  industry: string;
  region: string;
  type: string;
  specificType: string;
  status: string;
}

// 根据类型生成模拟数据
function getMockData(type: string): NotifyReportDto[] {
  const baseData = [
    {
      id: "1",
      notifyTime: "2024-01-15 10:30:00",
      notifyUnit: "广东省网信办",
      involvedUnit: "某教育机构",
      notifyTitle: "数据泄露安全事件通报",
      industry: "教育",
      region: "广州市",
      type: "数据安全",
      specificType: "数据泄露",
      status: "处理中",
    },
    {
      id: "2",
      notifyTime: "2024-01-14 14:20:00",
      notifyUnit: "工信部",
      involvedUnit: "某金融机构",
      notifyTitle: "网络攻击事件通报",
      industry: "金融",
      region: "深圳市",
      type: "网络安全",
      specificType: "恶意攻击",
      status: "已处理",
    },
    {
      id: "3",
      notifyTime: "2024-01-13 09:15:00",
      notifyUnit: "公安部网安局",
      involvedUnit: "某政府部门",
      notifyTitle: "系统漏洞安全通报",
      industry: "政府",
      region: "珠海市",
      type: "系统安全",
      specificType: "系统漏洞",
      status: "待处理",
    },
    {
      id: "4",
      notifyTime: "2024-01-12 16:45:00",
      notifyUnit: "广东省通管局",
      involvedUnit: "某通信运营商",
      notifyTitle: "服务中断事件通报",
      industry: "通信",
      region: "佛山市",
      type: "服务安全",
      specificType: "服务中断",
      status: "已关闭",
    },
    {
      id: "5",
      notifyTime: "2024-01-11 11:30:00",
      notifyUnit: "能源局",
      involvedUnit: "某电力公司",
      notifyTitle: "关键基础设施安全事件",
      industry: "能源",
      region: "东莞市",
      type: "基础设施",
      specificType: "设备故障",
      status: "处理中",
    },
  ];

  // 根据类型调整数据
  return baseData.map((item, index) => ({
    ...item,
    id: `${type}-${index + 1}`,
  }));
}

function getTypeLabel(type: string) {
  switch (type) {
    case "1":
      return "党政机关";
    case "2":
      return "媒体机构";
    case "3":
      return "公共服务";
    case "4":
      return "重点行业";
    default:
      return "防护对象";
  }
}

function MoreComp(props: IProps) {
  const { open, onCancel, type } = props;

  // 行业选项
  const industryOptions = useMemo(
    () => [
      { label: "金融", value: "finance" },
      { label: "教育", value: "education" },
      { label: "医疗", value: "medical" },
      { label: "能源", value: "energy" },
      { label: "交通", value: "transport" },
      { label: "通信", value: "communication" },
      { label: "政府", value: "government" },
      { label: "媒体", value: "media" },
    ],
    []
  );

  // 状态选项
  const statusOptions = useMemo(
    () => [
      { label: "待处理", value: "pending" },
      { label: "处理中", value: "processing" },
      { label: "已处理", value: "completed" },
      { label: "已关闭", value: "closed" },
    ],
    []
  );

  // 区域选项（简化的省市数据）
  const regionOptions = useMemo(
    () => [
      {
        title: "广东省",
        value: "guangdong",
        children: [
          { title: "广州市", value: "guangzhou" },
          { title: "深圳市", value: "shenzhen" },
          { title: "珠海市", value: "zhuhai" },
          { title: "佛山市", value: "foshan" },
          { title: "东莞市", value: "dongguan" },
        ],
      },
    ],
    []
  );

  const columns: ProColumns<NotifyReportDto>[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 80,
    },
    {
      dataIndex: "notifyTime",
      title: "通报时间",
      width: 120,
    },
    {
      dataIndex: "notifyUnit",
      title: "通报单位",
      width: 150,
    },
    {
      dataIndex: "involvedUnit",
      title: "涉事单位",
      width: 150,
    },
    {
      dataIndex: "notifyTitle",
      title: "通报标题",
      width: 200,
      ellipsis: true,
    },
    {
      dataIndex: "industry",
      title: "所属行业",
      width: 100,
    },
    {
      dataIndex: "region",
      title: "所属区域",
      width: 120,
    },
    {
      dataIndex: "type",
      title: "类型",
      width: 100,
    },
    {
      dataIndex: "specificType",
      title: "具体类型",
      width: 120,
    },
    {
      dataIndex: "isReal",
      title: "是否属实",
      width: 120,
    },
    {
      dataIndex: "status",
      title: "状态",
      width: 100,
    },
  ];

  // 将原 QueryFilter 的字段迁移至 ProTable 的 searchColumns
  const searchColumns: any[] = useMemo(
    () => [
      {
        dataIndex: "notifyTime",
        title: "通报时间",
        valueType: "dateRange",
      },
      {
        dataIndex: "notifyUnit",
        title: "通报单位",
        valueType: "text",
      },
      {
        dataIndex: "involvedUnit",
        title: "涉事单位",
        valueType: "text",
      },
      {
        dataIndex: "industry",
        title: "所属行业",
        valueType: "select",
        fieldProps: {
          options: industryOptions,
        },
      },
      {
        dataIndex: "region",
        title: "所属区域",
        valueType: "treeSelect",
        fieldProps: {
          treeData: regionOptions,
          placeholder: "请选择区域",
        },
      },
      {
        dataIndex: "status",
        title: "状态",
        valueType: "select",
        fieldProps: {
          options: statusOptions,
        },
      },
    ],
    [industryOptions, regionOptions, statusOptions]
  );

  const loadList: GetProp<typeof ProTable, "request"> = async () => {
    // 模拟数据生成
    const mockData = getMockData(type);

    return {
      data: mockData,
      total: mockData.length,
    };
  };

  return (
    <ProModal type={2} title={"重点防护对象"} open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        searchColumns={searchColumns}
        request={open ? loadList : undefined}
        scroll={{
          y: 480,
          x: 1200,
        }}
      />
    </ProModal>
  );
}

export default memo(MoreComp);
