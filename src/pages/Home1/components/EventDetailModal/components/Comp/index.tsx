import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import dictInfo from "@/dictInfo";
import managementService from "@/service/managementService";
import { useRequest } from "ahooks";
import { Descriptions } from "antd";
import type { DescriptionsProps } from "antd";
import saveAs from "file-saver";
import { useMemo } from "react";
import { encode } from "js-base64";

interface IProps extends CustomModalProps {
  notifyId?: string;
}

const COMPRESSED_FILE_TYPES = ["doc","docx","pdf"];

function Comp(props: IProps) {
  const { notifyId, onCancel, open } = props;

  const { data } = useRequest(
    () => managementService.eventNotifyInfo(notifyId!),
    {
      ready: open,
      refreshDeps: [notifyId],
    }
  );

  const levelName = useMemo(
    () =>
      dictInfo.realtimeEventLevel.find((val) => val.value === data?.eventLevel)
        ?.label,
    [data?.eventLevel]
  );

  const onPreview = async (item: API.Management.File) => {
    // attachmentFile 进行 base64编码，支持中文等字符
    const base64 = encode(`${process.env.downloadUrl}${item.attachmentFile}`);
    const previewUrl = `${process.env.previewUrl}/onlinePreview?url=${base64}`;
    window.open(previewUrl, "_blank");
  };

  const onFilePathPreview = async (item: API.Management.File) => {
    // 提取attachmentFile中filePath=后的字符然后进行编码
    const filePath = item.attachmentFile?.split("filePath=")[1];
    const base64 = encode(`${process.env.downloadUrl}${filePath}`);
    const previewUrl = `${process.env.previewUrl}/onlinePreview?url=${base64}`;
    window.open(previewUrl, "_blank");
  };

  const onDownload = async (item: API.Management.File) => {
    const downloadUrl = `${process.env.downloadUrl}${item.attachmentFile}`;
    window.open(downloadUrl, "_blank");
  };

  const items: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "所属区域",
      children: data?.area,
    },
    {
      key: "2",
      label: "事件等级",
      children: levelName,
    },
    {
      key: "3",
      label: "事件详情",
      children: data?.eventDetail,
      span: 2,
    },
    {
      key: "4",
      label: "事件描述",
      children: data?.eventDesc,
      span: 2,
    },
    {
      key: "5",
      label: "附件",
      children: (
        <div className="flex flex-col gap-[6px]">
          {data?.eventAttachmentList?.map((val, index) => (
            <div
              className="flex items-center gap-[28px]"
              key={`eventAttachment${index}`}
            >
              <span>
                {val.filename}.{val.fileType}
              </span>
              <div className="min-w-[100px] flex items-center gap-[6px]">
                {COMPRESSED_FILE_TYPES.includes(
                  val.fileType?.toLowerCase() as string
                ) && (
                  <span
                    className="text-[#395ABF] cursor-pointer"
                    onClick={() => {
                      if (val.attachmentFile) {
                        onPreview(val);
                      }
                    }}
                  >
                    预览
                  </span>
                )}
                <span
                  className="text-[#395ABF] cursor-pointer"
                  onClick={() => {
                    if (val.attachmentFile) {
                      // saveAs(
                      //   val.attachmentFile,
                      //   `${val.filename}.${val.fileType}`
                      // );
                      onDownload(val);
                    }
                  }}
                >
                  下载
                </span>
              </div>
            </div>
          ))}
        </div>
      ),
      span: 2,
    },
    {
      key: "5",
      label: "处置情况",
      span: 2,
      children: (
        <div className="grid gap-y-3">
          {data?.processInfoDtoList?.map((val, index) => (
            <div key={`processInfoDto${index}`}>
              <div>
                <span className="mr-3 text-[#395ABF]">{val.processTime}</span>
                {val.processName}
              </div>
              <div className="text-[#395ABF] leading-8">
                {val.disposalOpinion}
              </div>
              {val.attachmentList?.map((val, fileIndex) => (
                <div className="flex items-center gap-[28px]" key={`attachmentList${index}-${fileIndex}`}>
                  <span>
                    {val.filename}.{val.fileType}
                  </span>
                  <div className="flex items-center gap-[6px]">
                    {COMPRESSED_FILE_TYPES.includes(
                      val.fileType?.toLowerCase() as string
                    ) && (
                      <span className="text-[#395ABF] cursor-pointer" onClick={() => {
                        if (val.attachmentFile) {
                          onFilePathPreview(val);
                        }
                      }}>预览</span>
                    )}
                    <span className="text-[#395ABF] cursor-pointer" onClick={() => {
                      if (val.attachmentFile) {
                        saveAs(val.attachmentFile, `${val.filename}.${val.fileType}`);
                        // onDownload(val);
                      }
                    }}>下载</span>
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      ),
    },
  ];

  return (
    <ProModal
      type={2}
      title={`${data?.eventName} 事件通报`}
      open={open}
      onCancel={onCancel}
    >
      <Descriptions
        labelStyle={{
          width: 100,
          justifyContent: "flex-end",
        }}
        column={2}
        className="py-6 px-6 text-[30px]"
        items={items}
      />
    </ProModal>
  );
}
export default Comp;
