import BgImage from "@/components/BgImage";
import bg from "@/assets/1/cardBg.webp";
import Container from "@/pages/Home1/container";
import { NotifyStatusEnum } from "@/enum";

interface IProps {
  index: number;

  rowGap: number;

  itemHeight: number;

  item: API.Management.RealtimeEventDto;

  // 是否动画
  showAnimate?: boolean;
}
function ListItem(props: IProps) {
  const {
    rowGap,
    itemHeight,
    item: { unitName, eventName, notifyStatus, foundTime, notifyId },
  } = props;

  const { onShowDetail } = Container.useContainer();

  return (
    <BgImage
      style={{
        height: itemHeight,
        marginBottom: rowGap,
        backgroundSize: "100% 100%",
      }}
      url={bg}
      className="bg-[rgba(0,44,91,0.4)]"
    >
      <div className="flex items-center justify-between pt-[24px] pl-[28px] pr-[24px]">
        <span className="text-[20px] text-[#CCDFFF] pl-[16px] truncate">
          {unitName}
        </span>
        <span
          className="text-[rgba(41,149,255,0.54)] cursor-pointer min-w-fit ml-1"
          onClick={() => onShowDetail(notifyId)}
        >
          详情
        </span>
      </div>
      <div className="flex items-center justify-between pl-[30px] pr-[24px] gap-x-[20px] mt-[16px]">
        <span className="text-[#C5D0D4] truncate text-[16px]">{eventName}</span>
        {/* {notifyStatus === NotifyStatusEnum.Responded && (
          <span className="text-[14px] border border-solid border-[#A3D588] rounded text-[#A3D588] whitespace-nowrap px-[10px] h-[28px] flex items-center bg-[rgba(163,213,136,0.15)]">
            已响应
          </span>
        )}
        {notifyStatus === NotifyStatusEnum.Pending && (
          <span className="text-[14px] border border-solid border-[#F1BB80] rounded text-[#F1BB80] whitespace-nowrap px-[10px] h-[28px] flex items-center bg-[rgba(241,187,128,0.15)]">
            待响应
          </span>
        )}
        {notifyStatus === NotifyStatusEnum.Dealt && (
          <span className="text-[14px] border border-solid border-[#71B0EB] rounded text-[#71B0EB] whitespace-nowrap px-[10px] h-[28px] flex items-center bg-[rgba(113,176,235,0.15)]">
            已处置
          </span>
        )}
        {notifyStatus === NotifyStatusEnum.PendingDeal && (
          <span className="text-[14px] border border-solid border-[#FF4D4F] rounded text-[#FF4D4F] whitespace-nowrap px-[10px] h-[28px] flex items-center bg-[rgba(255,77,79,0.15)]">
            处置中
          </span>
        )} */}
      </div>
      <div className="text-[#C5D0D4] truncate text-[16px] mt-[4px] pl-[30px]">
        时间：{foundTime}
      </div>
    </BgImage>
  );
}
export default ListItem;
