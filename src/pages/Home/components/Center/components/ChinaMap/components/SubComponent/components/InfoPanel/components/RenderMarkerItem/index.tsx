import React from "react";
import { Html } from "@react-three/drei";
import bg from "@/assets/0/popBg.png";
import point from "@/assets/0/chinaPoint.png";
import type { DataItemConfig } from "../..";

interface IProps {
  item: DataItemConfig & {
    x: number;
    y: number;
  };
  index: number;
  intervalInfo: {
    interval: number | undefined;
    setInterval: (interval: number | undefined) => void;
    clear: () => void;
  };
  markerRef: React.MutableRefObject<HTMLDivElement[]>;
  selectMarkerRef: React.MutableRefObject<number | undefined>;
}

function RenderMarkerItem(props: IProps) {
  const { item, index, markerRef, selectMarkerRef, intervalInfo } = props;
  const { x, y, srcName, srcCode, attackCount } = item;
  const { interval, setInterval, clear } = intervalInfo;

  // 标牌样式
  const panelStyle = {
    width: "max-content",
    minWidth: 320,
    height: 164,
    background: `url(${bg}) 100% 100% / 100% 100% no-repeat`,
    display: "none",
    flexDirection: "column",
    pointerEvents: "none",
    padding: "0 10%",
    transform: "translate(-0%, -72%)",
  } as React.CSSProperties;

  return (
    <React.Fragment>
      <Html
        position={[x, y, 0]}
        transform
        sprite
        scale={[7, 7, 7]}
        userData={{
          index,
        }}
        name={`${srcCode}-subGroup`}
      >
        <div
          className="flex flex-col items-center mt-[-26px] cursor-pointer"
          onPointerEnter={() => {
            // 判断当前是否有定时器启动
            if (interval) {
              // 清除定时器
              clear();
              setInterval(undefined);
              if (selectMarkerRef.current !== undefined) {
                // 获取定时器选中的ref
                const intervalActiveRef =
                  markerRef.current[selectMarkerRef.current];
                intervalActiveRef.style.display = "none";
              }
            }
            // 获取当前选中的ref
            const selectRef = markerRef.current[index];
            // 展示面板
            selectRef.style.display = "flex";
          }}
          onPointerLeave={() => {
            // 如果没有定时器启动 则开启定时器
            if (interval === undefined) {
              setInterval(2000);
            }
            // 获取当前选中的ref
            const selectRef = markerRef.current[index];
            // 隐藏面板
            selectRef.style.display = "none";
          }}
        >
          <div className="bg-[rgb(16,39,74)] border border-white px-[10px] h-[30px] flex items-center">
            {srcName}
          </div>
          <img
            src={point}
            className="w-[40px] h-[40px] relative mt-1 opacity-80"
          />
        </div>
      </Html>
      <Html
        position={[x, y, 10]}
        transform
        sprite
        scale={[7, 7, 7]}
        userData={{
          index,
        }}
        pointerEvents="none"
        name={`${srcCode}-subGroup`}
      >
        <div
          style={panelStyle}
          ref={(ref) => {
            markerRef.current[index] = ref!;
          }}
        >
          <div className="ml-[-6px] text-[20px] mt-3">攻击线路</div>
          <div className="grid grid-cols-3 h-[104px] items-center flex-nowrap">
            <div className="text-center">
              <div className="color-secondary text-[18px]">源地址</div>
              <div className="text-[22px] color-text mt-1">{srcName ?? "--"}</div>
            </div>
            <div className="text-center">
              <div className="color-secondary text-[18px]">目的地</div>
              <div className="text-[22px] color-text mt-1">广东省</div>
            </div>
            <div className="text-center">
              <div className="color-secondary text-[18px]">攻击次数</div>
              <div className="text-[22px] color-text mt-1">{attackCount}</div>
            </div>
          </div>
        </div>
      </Html>
    </React.Fragment>
  );
}

export default RenderMarkerItem;
