import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import resourcesService from "@/service/resourcesService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { useMemo } from "react";

// 添加自定义属性，避免空接口警告
interface IProps extends CustomModalProps {
  key?: string;
}

function MoreComp(props: IProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = useMemo(() => {
    return [
      {
        dataIndex: "dataIndex",
        title: "序号",
        align: "center",
        width: 100,
      },
      {
        title: "地市",
        dataIndex: "name",
      },
      {
        dataIndex: "value",
        title: "资产数",
      },
    ];
  }, []);

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await resourcesService.cityTopList({
      startTime,
      endTime,
      ...params,
    });
    
    const data = res.cityTopList?.map((item) => ({
      name: item.cityName,
      value: item.count,
    })) || [];
    
    return {
      data,
      total: res.total,
    };
  };

  return (
    <ProModal title="地市资产排名" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total: number) => <span>共 {total} 条记录</span>,
        }}
      />
    </ProModal>
  );
}

export default MoreComp; 