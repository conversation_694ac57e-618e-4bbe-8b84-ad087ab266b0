import Card from "@/components/Card";
import { motion } from "framer-motion";
import useListSize from "@/hooks/useListSize";
import useListAnimate from "@/hooks/useListAnimate";
import {
  TableAnimationConnectModeEnum,
  TableAnimationModeEnum,
  TableAnimationTypeEnum,
} from "@/enum";
import { useMemo, useState } from "react";
import ListItem from "./components/ListItem";
import useVisible from "@/hooks/useVisible";
import MoreComp from "./components/MoreComp";
import resourcesService from "@/service/resourcesService";
import { useRequest } from "ahooks";
import { useTime } from "@/store/useTime";
import { Pagination } from "antd";

function AssetsDistribution() {
  const headerHeight = 40;

  const height = 410;

  const size = 5;

  const rowGap = 10;

  const width = 456;

  const { itemHeight, listHeight } = useListSize({
    headerHeight,
    height,
    size,
    rowGap,
  });

  const animationConfig = {
    show: false, // 是否显示动画
    type: TableAnimationTypeEnum.Single, // 类型
    connectMode: TableAnimationConnectModeEnum.Continuous, // 衔接方式
    animateMode: TableAnimationModeEnum.Flip, // 动画形式
    interval: 3, //
    backgroundFixed: false,
  };

  const { startTime, endTime } = useTime();

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(size);

  const { data, loading } = useRequest(
    () =>
      resourcesService.industryAssetsCountList({
        page: current,
        size: pageSize,
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
    }
  );

  const list = useMemo(
    () => data?.industryAssetsCountList ?? [],
    [data?.industryAssetsCountList]
  );

  const total = useMemo(() => data?.total ?? 0, [data?.total]);

  const {
    controls,
    firstControls,
    dataIndexList,
    showAnimate,
    beforeIndexList,
  } = useListAnimate({
    data: list,
    size,
    rowGap,
    listHeight,
    itemHeight,
    animationConfig,
  });

  const maxValue = useMemo(() => {
    const countList = list?.map((val) => val.count) ?? [];
    return Math.max(...countList);
  }, [list]);

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className="absolute right-[46px] top-[130px] h-[515px]">
      <MoreComp key={visibleKey} open={open} onCancel={setFalse} />
      <Card
        headerType={1}
        title="行业资产分布"
        onMore={setTrue}
        loading={loading}
      >
        <div
          style={{
            width,
            height,
          }}
          className="relative -top-2"
        >
          <div
            className="flex color-secondary items-center gap-x-3"
            style={{
              height: headerHeight,
            }}
          >
            <div className="w-[76px]">序号</div>
            <div className="flex-1">行业名称</div>
            <div className="w-[64px]">资产数</div>
          </div>
          {/* 列表 */}
          <div
            className="overflow-hidden relative"
            style={{
              height: listHeight,
              width: width + 40, // 为了进度图标能显示出来
            }}
          >
            <motion.div
              className="absolute"
              style={{
                width,
                height: listHeight,
              }}
              animate={controls}
            >
              {showAnimate && (
                <motion.div animate={firstControls}>
                  {beforeIndexList?.map((val) => (
                    <ListItem
                      key={`before${val}`}
                      index={val}
                      rowGap={rowGap}
                      item={list[val]}
                      showAnimate={false}
                      max={maxValue}
                      current={current}
                      size={pageSize}
                      itemHeight={itemHeight}
                    />
                  ))}
                </motion.div>
              )}
              {dataIndexList.map((val) => (
                <ListItem
                  key={`listItem${val + 1}`}
                  index={val}
                  rowGap={rowGap}
                  current={current}
                  size={pageSize}
                  item={list[val]}
                  max={maxValue}
                  itemHeight={itemHeight}
                />
              ))}
            </motion.div>
          </div>
        </div>
        {total > pageSize && (
          <div className="flex justify-end items-center mt-4">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, pageSize) => {
                setCurrent(page);
                setPageSize(pageSize);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default AssetsDistribution;
