import Card from "@/components/Card";
import ProTable from "@/components/Table";
import resourcesService from "@/service/resourcesService";
import type { ProColumns } from "@ant-design/pro-table";
import Container from "../../container";
import { useMemo } from "react";
import arr from "../../moke/data";
import { useRequest } from "ahooks";

function UnitAssetDetail() {
  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 80,
    },
    {
      dataIndex: "organizationName",
      title: "单位名称",
      width: 200,
      ellipsis: true,
    },
    {
      dataIndex: "organizationType",
      title: "单位类型",
      width: 120,
    },
    {
      dataIndex: "industryName",
      title: "行业名称",
      width: 120,
    },
    {
      dataIndex: "area",
      title: "所属地区",
      width: 100,
    },
    {
      dataIndex: "sysName",
      title: "设施名称",
      width: 150,
      ellipsis: true,
    },
    {
      dataIndex: "ipAddress",
      title: "IP地址",
      width: 130,
    },
    {
      dataIndex: "assetsTypeName",
      title: "资产类型",
      width: 100,
    },
    {
      dataIndex: "domain",
      title: "域名",
      width: 150,
      ellipsis: true,
    },
    {
      dataIndex: "sysType",
      title: "业务类型",
      width: 120,
    },
  ];

  const { onShowList } = Container.useContainer();

  const { data } = useRequest(
    async () =>
      await resourcesService.baseAssetsList({
        page: "-1",
        size: "9999",
      })
  );

  const dataSource = useMemo(() => {
    return [...arr, ...(data?.baseAssetsList || [])];
  }, [data?.baseAssetsList]);

  return (
    <div className="absolute left-[542px] top-[570px] w-[848px] h-[200px]">
      <Card
        headerType={2}
        title="单位资产明细表"
        bodyClass="!p-0"
        onMore={() => onShowList({})}
      >
        <ProTable
          columns={columns}
          dataSource={dataSource}
          search={false}
          toolBarRender={false}
          scroll={{
            y: 330,
            x: 848,
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: false,
            showTotal: (total: number) => `共 ${total} 条记录`,
          }}
        />
      </Card>
    </div>
  );
}

export default UnitAssetDetail;
