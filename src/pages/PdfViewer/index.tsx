import managementService from "@/service/managementService";
import React, { useState } from "react";

export default function PdfViewer() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  const onHandlePreview = async () => {
    try {
      setLoading(true);
      setError("");
      
      const res = await managementService.previewPdf({
        filePath:
          "/home/<USER>/screen/data/response.pdf",
        fileType: "pdf",
      });

      // 检查返回的blob是否有效
      if (!res || !(res instanceof Blob)) {
        throw new Error("返回的数据不是有效的blob格式");
      }

      // 创建blob URL
      const blobUrl = URL.createObjectURL(res);
      
      // 在新窗口中打开PDF预览
      const newWindow = window.open(blobUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
      
      if (!newWindow) {
        throw new Error("无法打开新窗口，请检查浏览器弹窗设置");
      }

      // 监听新窗口关闭事件，清理blob URL
      newWindow.addEventListener('beforeunload', () => {
        URL.revokeObjectURL(blobUrl);
      });
      
      console.log("PDF预览已在新窗口打开:", blobUrl);
    } catch (err) {
      console.error("PDF预览失败:", err);
      setError(err instanceof Error ? err.message : "预览失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <React.Fragment>
      <div style={{ padding: "20px" }}>
        <button 
          type="button"
          onClick={onHandlePreview}
          disabled={loading}
          style={{
            padding: "10px 20px",
            backgroundColor: loading ? "#ccc" : "#1890ff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: loading ? "not-allowed" : "pointer",
            marginBottom: "20px"
          }}
        >
          {loading ? "加载中..." : "点击预览PDF"}
        </button>

        {error && (
          <div style={{ 
            color: "red", 
            marginBottom: "20px",
            padding: "10px",
            border: "1px solid red",
            borderRadius: "4px",
            backgroundColor: "#fff2f0"
          }}>
            错误: {error}
          </div>
        )}

        <div style={{ 
          padding: "10px",
          backgroundColor: "#f5f5f5",
          borderRadius: "4px",
          fontSize: "14px",
          color: "#666"
        }}>
          <p>点击按钮后，PDF将在新窗口中打开预览。</p>
          <p>如果新窗口没有打开，请检查浏览器的弹窗阻止设置。</p>
        </div>
      </div>
    </React.Fragment>
  );
}
