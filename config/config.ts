import { defineConfig } from "umi";

const isDev = process.env.NODE_ENV === "development";

export default defineConfig({
  routes: [
    { path: "/", redirect: "/index" },
    {
      path: "/index",
      component: "Index/index",
      layout: false,
    },
    {
      path: "/home",
      component: "Home",
    },
    {
      path: "/home1",
      component: "Home1",
    },
    {
      path: "/home2",
      component: "Home2",
    },
    {
      path: "/home3",
      component: "Home3",
    },
    {
      path: "/home4",
      component: "Home4",
    },
    {
      path: "/pdf-viewer",
      component: "PdfViewer",
      layout: false,
    }
  ],
  proxy: {
    "/api": {
      target: "http://192.168.0.130:18088/screenGatewayService",
      // target: 'https://qr.hexinjingu.com/qrcodeServer/hxqr',
      changeOrigin: true,
      // 支持https
      //   secure: true,
      pathRewrite: { "^/api": "" },
    },
  },
  esbuildMinifyIIFE: true,
  // base:"./",
  publicPath: isDev ? "/" : "./",
  mfsu: false,
  npmClient: "pnpm",
  hash: true,
  history: {
    type: "hash",
  },
  request: {
    dataField: "data",
  },
  tailwindcss: {},
  plugins: ["@umijs/plugins/dist/tailwindcss", "@umijs/plugins/dist/request"],
});
